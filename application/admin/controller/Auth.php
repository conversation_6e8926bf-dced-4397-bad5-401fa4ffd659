<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\service\WechatService;

class Auth extends Controller
{
    protected $openid;
    protected function initialize()
    {
        parent::initialize();
        $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        $this->openid = session("$sessionKey.id");
    }

    /**
     * 经销商注册微信授权
     *
     * @return void
     */
    public function dealer()
    {
        // 接收公众号ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-101');
        }
        // 验证state参数
        if (!empty($state) && !WechatService::validateState($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-102');
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是经销商，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-103');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/dealer');
        }
        return $this->fetch();
    }

    /**
     * 群管注册微信授权
     *
     * @return void
     */
    public function groupMgr()
    {
        // 接收经销商ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-104');
        }
        // 验证state参数
        if (!empty($state) && !WechatService::validateStateToUser($state, 1)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-105');
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是群管，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-106');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/groupMgr');
        }
        return $this->fetch("group");
    }

    /**
     * 会员注册微信授权
     *
     * @return void
     */
    public function member()
    {
        // 接收群管ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-107');
        }
        // 验证state参数
        if (!empty($state) && !WechatService::validateStateToUser($state, 2)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-108');
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是会员，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-109');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
            return redirect('register/member');
        }
        return $this->fetch("member");
    }

    // 公众号授权回调
    public function oauthCallback()
    {
        try {
            $app = WechatService::getOfficialAccount();
            $user = $app->oauth->user();

            // 保存用户信息
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
            session($sessionKey, $user->toArray());

            $target_url = session('target_url');
            return redirect($target_url);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '微信授权失败，请重试');
        }
    }

    // 企业微信授权回调
    public function workCallback()
    {
        try {
            $configId = Request::param('config_id', 2); // 默认使用ID为2的配置

            $app = WechatService::getWork($configId);
            $user = $app->oauth->user();

            // 保存用户信息
            session('wechat_user', $user->toArray());

            // 获取跳转目标URL
            $targetUrl = session('target_url');
            if (empty($targetUrl)) {
                $targetUrl = 'index/index'; // 默认跳转到首页
            }

            // 跳转回原页面
            return redirect($targetUrl);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '企业微信授权失败，请重试');
        }
    }
}
