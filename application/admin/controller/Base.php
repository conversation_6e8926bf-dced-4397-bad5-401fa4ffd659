<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\exception\HttpResponseException; // 引入 HttpResponseException

class Base extends Controller
{
    protected $accountInfo;
    protected $openid;

    protected function initialize()
    {
        parent::initialize();

        $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        $openid = session("$sessionKey.id");

        $openid = 'oJYdD6kvLJQgntumtjpTjau0LY9s';

        if (empty($openid)) {
            $redirect = redirect('error/index')->with('error_msg', '无效的授权请求-201');
            throw new HttpResponseException($redirect);
        }

        //获取当前登录账号
        $this->accountInfo = Db::name('org_wxuser')->where(['openid' => $openid])->find();
        // 增加对 accountInfo 的判断，如果获取不到用户信息也应该处理
        if (empty($this->accountInfo)) {
            $redirect = redirect('error/index')->with('error_msg', '无法获取用户信息，请重新授权登录');
            throw new HttpResponseException($redirect);
        }
        $this->openid = $this->accountInfo['openid'];
        $this->assign('accountInfo', $this->accountInfo);

        // 时间戳
        $this->assign('time', time());
    }
}
