<?php
namespace app\admin\controller;

use think\facade\Request;
use think\exception\HttpResponseException; // 引入 HttpResponseException
use app\admin\model\OpenSea;

class Index extends Base
{
    protected function initialize()
    {
        parent::initialize();

        // 检查用户角色并进行适当跳转
        $OpenSea = new OpenSea();
        $target_url = $OpenSea->checkUserRoleAndRedirect($this->openid);

        // 获取当前请求的控制器
        $currentController = strtolower(Request::controller());

        // 避免死循环：如果目标URL是index/index且当前就在Index控制器，则不进行重定向
        if (!empty($target_url)) {
            $targetRoute = strtolower(trim($target_url, '/'));

            // 如果目标是index/index且当前在Index控制器，则跳过重定向避免死循环
            if (!($targetRoute === 'index/index' && $currentController === 'index')) {
                $redirect = redirect($target_url);
                throw new HttpResponseException($redirect);
            }
        }

        // 设置当前标签
        $this->assign('navtab', 'index');
    }

    public function index()
    {
        return $this->fetch();
    }

    function index2(){
        echo '[HOME]服务正常运行中...';
    }

    /**
     * 清空系统缓存
     * @param NULL
     */
    public function clearCache(){
        import("Org.Util.Dir");
        $dirs = array ();
        $rootdirs = $this->scan_dir( RUNTIME_PATH."*" );
        $noneed_clear = array(".","..");
        $rootdirs = array_diff($rootdirs, $noneed_clear);
        foreach ( $rootdirs as $dir ) {
            if ($dir != "." && $dir != "..") {
                $dir = RUNTIME_PATH . $dir;
                if (is_dir ( $dir )) {
                    $tmprootdirs = $this->scan_dir($dir."/*" );
                    foreach ( $tmprootdirs as $tdir ) {
                        if ($tdir != "." && $tdir != "..") {
                            $tdir = $dir . '/' . $tdir;
                            if (is_dir ( $tdir )) {
                                array_push ( $dirs, $tdir );
                            }else{
                                @unlink($tdir);
                            }
                        }
                    }
                }else{
                    @unlink($dir);
                }
            }
        }
        $dirtool = new \Dir("");
        foreach ( $dirs as $dir ) {
            $dirtool->delDir($dir);
        }
        $this->success('清空系统缓存成功!',U('index'));
    }
}