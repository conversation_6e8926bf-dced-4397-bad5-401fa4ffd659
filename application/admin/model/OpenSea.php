<?php

namespace app\admin\model;

use think\Model;
use think\Db;
use think\facade\Request;

class OpenSea extends Model
{
    /**
     * 检查用户角色并进行适当跳转
     *
     * @param string $openid 用户openid
     */
    public function checkUserRoleAndRedirect($openid)
    {
        $target_url = session('target_url');

        $sessionKey = 'wechat_user_' . session('wx_gzh_id');

        // 查询用户信息
        $userRow = Db::name('org_wxuser')->where(['openid' => $openid])->find();

        if (empty($userRow) && empty(session('wx_dealer_id')) && empty(session('wx_group_id'))) {
            // 用户不存在，且不属于任何经销商和群管，创建为公海会员
            $insertData = [
                'type' => 4,
                'wxgzh_id' => session('wx_gzh_id'),
                'org_id' => getWxgzhBindMchId(session('wx_gzh_id')),
                'agency_id' => 0,
                'tube_id' => 0,
                'openid' => $openid,
                'user_name' => base64_encode(session("$sessionKey.nickname")),
                'nickname' => base64_encode(session("$sessionKey.nickname")),
                'avatar' => session("$sessionKey.avatar"),
                'ip' => Request::ip(),
                'last_visit' => time(),
                'status' => 9,
                'create_time' => time()
            ];

            $userId = Db::name('org_wxuser')->insertGetId($insertData);
            if ($userId) {
                $userRow = Db::name('org_wxuser')->where('user_id', $userId)->find();
            }
        }

        // 根据用户类型进行跳转
        switch ($userRow['type']) {
            case '1':
                // 经销商
                if ($userRow['status'] == 1 || $userRow['status'] == 5) {
                    // 待审核或被拒绝，跳转到申请页面
                    $target_url = 'register/dealerApply';
                } elseif ($userRow['status'] == 9) {
                    // 已通过，跳转到首页
                    $target_url = 'index/index';
                }
                break;

            case '2':
                // 群管
                if ($userRow['status'] == 1 || $userRow['status'] == 5) {
                    // 待审核或被拒绝，跳转到申请页面
                    $target_url = 'register/groupApply';
                } elseif ($userRow['status'] == 9) {
                    // 已通过，跳转到首页
                    $target_url = 'index/index';
                }
                break;

            case '3':
            case '4':
                // 会员或公海会员，跳转到会员页面
                $target_url = 'user/info';
                break;
        }

        // 默认跳转到原页面
        return $target_url;
    }
}
