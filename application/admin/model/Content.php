<?php

namespace app\admin\model;

use think\Model;
use think\Db;
use think\facade\Request;

/**
 * 内容模型类
 *
 * 主要功能：
 * 1. 获取内容列表数据
 * 2. 处理内容相关的业务逻辑
 */
class Content extends Model
{
    /**
     * 用户类型常量
     */
    const TYPE_DEALER = 1;    // 经销商
    const TYPE_GROUP_MGR = 2; // 群管
    const TYPE_MEMBER = 3;    // 会员
    const TYPE_PUBLIC = 4;    // 公海会员

    /**
     * 获取内容列表数据
     *
     * @param array $accountInfo 用户账号信息
     * @param array $filter 筛选条件
     * @return array 内容列表数据
     */
    public function getContentList($accountInfo, $filter = [])
    {
        if (empty($accountInfo)) {
            return ['code' => 400, 'msg' => '无法获取用户信息，请重新授权登录'];
        }

        // 根据用户类型获取经销商ID
        $agency_id_result = $this->getAgencyIdByUserType($accountInfo);
        if (is_array($agency_id_result) && isset($agency_id_result['code']) && $agency_id_result['code'] != 200) {
            return $agency_id_result;
        }
        $agency_id = $agency_id_result['data'];

        // 处理筛选条件
        $filter['period_id'] = isset($filter['period_id']) ? $filter['period_id'] : Request::param('period_id', 0);

        // 获取营期组
        $periodRows = $this->getPeriodList($accountInfo, $agency_id);
        if (!empty($periodRows)) {
            // 构建查询条件
            $cond = $this->buildQueryCondition($filter, $periodRows);
            if (!empty($cond)) {
                // 获取课程列表
                $items = $this->getCourseItems($cond);
            }
        }

        return [
            'code' => 200,
            'msg' => 'success',
            'data' => [
                'periodRows' => $periodRows ?? [],
                'items' => $items ?? [],
                'filter' => $filter
            ]
        ];
    }

    /**
     * 根据用户类型获取经销商ID
     *
     * @param array $accountInfo 用户账号信息
     * @return int|array 经销商ID或错误信息
     */
    private function getAgencyIdByUserType($accountInfo)
    {
        $type = $accountInfo['type'];

        switch ($type) {
            case self::TYPE_DEALER:
                return ['code' => 200, 'msg' => 'success', 'data' => Db::name('org_agency')->where(['wx_user_id' => $accountInfo['user_id']])->value('agency_id')];

            case self::TYPE_GROUP_MGR:
            case self::TYPE_MEMBER:
                return ['code' => 200, 'msg' => 'success', 'data' => $accountInfo['agency_id']];

            case self::TYPE_PUBLIC:
            default:
                return ['code' => 403, 'msg' => '您没有权限访问', 'data' => null];
        }
    }

    /**
     * 获取营期列表
     *
     * @param array $accountInfo 用户账号信息
     * @param int $agency_id 经销商ID
     * @return array 营期列表
     */
    private function getPeriodList($accountInfo, $agency_id)
    {
        $map = [
            'org_id' => $accountInfo['org_id'],
            'wxgzh_id' => $accountInfo['wxgzh_id'],
            'status' => 1,
            'del' => 0,
        ];

        $field = 'period_id,period_name';
        return Db::name('org_period')
            ->field($field)
            ->where($map)
            ->whereRaw("FIND_IN_SET($agency_id, agency_ids)")
            ->select();
    }

    /**
     * 构建查询条件
     *
     * @param array $filter 筛选条件
     * @param array $periodRows 营期列表
     * @return array 查询条件
     */
    private function buildQueryCondition($filter, $periodRows)
    {
        $cond = [];

        if (!empty($filter['period_id'])) {
            $cond[] = ['t1.period_id', '=', $filter['period_id']];
        } else {
            $period_ids = array_column($periodRows, 'period_id');
            if (!empty($period_ids)) {
                $cond[] = ['t1.period_id', 'in', $period_ids];
            }
        }

        if (!empty($cond)) {
            $cond[] = ['t1.status', '=', 1];
            $cond[] = ['t1.del', '=', 0];
        }

        return $cond;
    }

    /**
     * 获取课程项目列表
     *
     * @param array $cond 查询条件
     * @return array 课程项目列表
     */
    private function getCourseItems($cond)
    {
        $item_field = 't1.item_id,t1.item_name,t1.start_time,t1.end_time,t1.create_time,t1.status,t2.course_id,t2.course_name,t2.show_progress,t2.allow_pause,t2.finish_play,t2.prize_type,t2.cover_url,t3.period_name,t3.period_id,t4.duration';

        return Db::name('org_period_item t1')
            ->join('ksd_org_course t2', 't1.course_id = t2.course_id', 'LEFT')
            ->join('ksd_org_period t3', 't1.period_id = t3.period_id', 'LEFT')
            ->join('ksd_org_video t4', 't1.video_id = t4.video_id', 'LEFT')
            ->field($item_field)
            ->where($cond)
            ->select();
    }
}
