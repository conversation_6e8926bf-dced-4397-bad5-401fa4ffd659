<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{:Config('app_name')}} - 课程详情</title>
    {{include file="common/content"}}
    <link rel="stylesheet" href="__PUBLIC__/css-build/course.css">
    <link rel="stylesheet" href="https://g.alicdn.com/apsara-media-box/imp-web-player/2.25.1/skins/default/aliplayer-min.css" /> 
    <script charset="utf-8" type="text/javascript" src="https://g.alicdn.com/apsara-media-box/imp-web-player/2.25.1/aliplayer-min.js"></script>  
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 主内容区域 -->
    <div class="pb-24 px-4">
        <!-- 用户信息 -->
        <div class="flex items-center mt-4">
            <div class="w-12 h-12 rounded-full overflow-hidden">
                <img src="{{$accountInfo.avatar}}"
                    alt="用户头像" class="w-full h-full object-cover">
            </div>
            <div class="ml-3">
                <div class="text-base font-medium">{{:base64_decode($accountInfo['user_name'])}}</div>
                <div class="text-xs text-gray-500">ID:{{$accountInfo.user_id}}</div>
            </div>
        </div>
        <!-- 课程视频封面 -->
        <div class="mt-4 relative rounded-lg overflow-hidden shadow-sm">
            <div id="J_prismPlayer">
                <input type="hidden" name="play_url" value="{{$playURL}}">
                <input type="hidden" name="cover_url" value="{{$coverURL}}">
                <input type="hidden" name="finish_play" value="{{$courseInfo.finish_play}}">
            </div>
        </div>
        <!-- 课程标题 -->
        <div class="mt-4 text-xl font-bold">{{$courseInfo.item_name}}</div>
        <!-- 选项卡导航 -->
        <div class="mt-6 flex border-b border-gray-200">
            <div class="tab-active pb-2 mr-8 cursor-pointer" data-tab="exam">考核</div>
            <div class="text-gray-500 pb-2 cursor-pointer" data-tab="intro">课程介绍</div>
        </div>
        <!-- 考核内容区域 -->
        <div id="exam-content">
            <!-- 进度显示 -->
            <!-- <div class="mt-6 flex justify-between items-center">
                <div class="text-2xl font-bold text-primary">0</div>
                <div class="flex-1 mx-4">
                    <div class="progress-bar">
                        <div class="progress-value" style="width: 0%"></div>
                    </div>
                </div>
                <div class="text-2xl font-bold">3</div>
            </div> -->
            <!-- 测验内容 -->
            <div class="mt-6">
                <div class="question-container" data-question="1">
                    <div class="bg-orange-100 text-orange-800 px-3 py-1 rounded-md inline-block text-sm mb-3">单选题</div>
                    <div class="text-base mb-6">
                        {{$courseInfo.question_name}}
                    </div>
                    <!-- 选项 -->
                    <div class="space-y-3">
                        {{volist name="courseInfo.question_item" id="vo"}}
                        <div>
                            <input type="radio" name="answer" id="option-{{$vo.item_id}}" value="{{$vo.item_id}}" {{if $userAnswer.answer_id == $vo.item_id}}checked{{/if}}>
                            <label for="option-{{$vo.item_id}}" class="radio-label">
                                <div class="radio-custom"></div>
                                <div class="flex items-center">
                                    <span class="text-gray-500 mr-4">{{$vo.option_name}}</span>
                                    <span>{{$vo.option_value}}</span>
                                </div>
                            </label>
                        </div>
                        {{/volist}}
                    </div>
                </div>
            </div>
        </div>
        <!-- 课程介绍内容区域 -->
        <div id="intro-content" class="hidden">
            <div class="mt-6">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h3 class="text-lg font-medium mb-4">课程简介</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        {{$courseInfo.desc}}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部提交按钮 -->
    {{if $accountInfo.type == 3}}
    <div class="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 p-4">
        <button id="next-btn"
            class="w-full py-3 bg-gray-300 text-white font-medium rounded-button flex items-center justify-center"
            disabled>
            提交
        </button>
    </div>
    {{/if}}

    <script>
        $(function() {
            // 获取课程项目ID
            const itemId = "{{$courseInfo.item_id}}";

            // 初始化变量
            let userAnswer = JSON.parse('{{:json_encode($userAnswer?:[])}}');
            let selectedAnswerId = userAnswer && userAnswer.answer_id ? userAnswer.answer_id : null;
            const nextButton = $('#next-btn');
            const tabs = $('[data-tab]');
            const examContent = $('#exam-content');
            const introContent = $('#intro-content');

            // 切换标签页
            function switchTab(tabType) {
                tabs.removeClass('tab-active').addClass('text-gray-500');
                $(`[data-tab="${tabType}"]`).addClass('tab-active').removeClass('text-gray-500');

                if (tabType === 'exam') {
                    examContent.removeClass('hidden');
                    introContent.addClass('hidden');
                    // 只有未完成时才显示按钮
                    if (!(userAnswer && userAnswer.is_correct == 1)) {
                        nextButton.parent().removeClass('hidden');
                    } else {
                        nextButton.parent().addClass('hidden');
                    }
                    syncButtonState();
                } else {
                    examContent.addClass('hidden');
                    introContent.removeClass('hidden');
                    nextButton.parent().addClass('hidden');
                }
            }

            // 同步提交按钮状态
            function syncButtonState() {
                if (selectedAnswerId) {
                    nextButton.prop('disabled', false)
                        .removeClass('bg-gray-300')
                        .addClass('bg-primary');
                } else {
                    nextButton.prop('disabled', true)
                        .removeClass('bg-primary')
                        .addClass('bg-gray-300');
                }
            }

            // 处理单选按钮变化
            $('input[type="radio"]').on('change', function() {
                selectedAnswerId = $(this).val();
                syncButtonState();
            });

            // 处理提交按钮点击
            nextButton.on('click', function() {
                if (!selectedAnswerId) {
                    return;
                }

                // 如果用户已经答题且答案正确，直接显示完成状态
                if (userAnswer && userAnswer.answer_id && userAnswer.is_correct == 1) {
                    showCompletionStatus();
                    return;
                }

                // 如果用户已经答题但答案错误，且选择的还是同一个错误答案，直接显示结果
                if (userAnswer && userAnswer.answer_id && userAnswer.is_correct == 0 &&
                    selectedAnswerId == userAnswer.answer_id) {
                    showResultDialog(false, userAnswer);
                    return;
                }

                // 显示加载状态
                nextButton.prop('disabled', true)
                    .text('提交中...');

                // 发送答题请求
                $.ajax({
                    url: '/Course/submitAnswer',
                    type: 'POST',
                    data: {
                        item_id: itemId,
                        answer_id: selectedAnswerId
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 200) {
                            // 更新用户答题记录
                            userAnswer = res.data;

                            // 答题成功
                            showResultDialog(res.data.is_correct, res.data);
                        } else {
                            // 答题失败
                            showErrorDialog(res.msg || '提交失败，请重试');
                        }

                        // 恢复按钮状态
                        nextButton.prop('disabled', false)
                            .text('提交');
                        syncButtonState();
                    },
                    error: function() {
                        // 网络错误
                        showErrorDialog('网络错误，请稍后重试');

                        // 恢复按钮状态
                        nextButton.prop('disabled', false)
                            .text('提交');
                        syncButtonState();
                    }
                });
            });

            // 显示结果对话框
            function showResultDialog(isCorrect, resultData) {
                // 创建对话框
                const dialog = $('<div>').addClass('fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50');

                // 设置对话框内容
                let dialogContent = '';
                if (isCorrect) {
                    dialogContent = `
                        <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                                    <i class="ri-check-line ri-2x text-green-500"></i>
                                </div>
                            </div>
                            <h3 class="text-lg font-medium text-center mb-4">回答正确</h3>
                            <p class="text-gray-600 text-center mb-6">恭喜您答对了问题！</p>
                            <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                        </div>
                    `;
                } else {
                    // 使用服务器返回的正确答案文本
                    const correctOptionText = resultData.correct_option_value;

                    dialogContent = `
                        <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                                    <i class="ri-close-line ri-2x text-red-500"></i>
                                </div>
                            </div>
                            <h3 class="text-lg font-medium text-center mb-4">回答错误</h3>
                            <p class="text-gray-600 text-center mb-2">正确答案是：</p>
                            <p class="text-primary text-center font-medium mb-6">${correctOptionText}</p>
                            <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                        </div>
                    `;
                }

                dialog.html(dialogContent);

                // 添加到页面
                $('body').append(dialog);

                // 绑定关闭事件
                dialog.find('.close-dialog').on('click', function() {
                    dialog.remove();

                    // 如果答对了，可以跳转到下一个页面或显示完成状态
                    if (isCorrect) {
                        // 这里可以添加跳转逻辑或显示完成状态
                        showCompletionStatus();
                    }
                });
            }

            // 显示错误对话框
            function showErrorDialog(message) {
                const dialog = $('<div>').addClass('fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50');

                dialog.html(`
                    <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                                <i class="ri-error-warning-line ri-2x text-red-500"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-center mb-4">提交失败</h3>
                        <p class="text-gray-600 text-center mb-6">${message}</p>
                        <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                    </div>
                `);

                $('body').append(dialog);

                dialog.find('.close-dialog').on('click', function() {
                    dialog.remove();
                });
            }

            // 显示完成状态
            function showCompletionStatus() {
                // 隐藏答题区域
                examContent.html(`
                    <div class="mt-10 text-center">
                        <div class="flex items-center justify-center">
                            <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="ri-check-double-line ri-3x text-green-500"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-medium mt-4">课程完成</h3>
                        <p class="text-gray-500 mt-2">您已成功完成本课程的学习</p>
                    </div>
                `);

                // 隐藏提交按钮
                nextButton.parent().addClass('hidden');
            }

            // 标签页切换事件
            tabs.on('click', function() {
                const tabType = $(this).data('tab');
                switchTab(tabType);
            });

            // 如果有用户答题记录，初始化选中状态
            if (userAnswer && userAnswer.answer_id) {
                // 选中用户之前选择的答案
                const answerRadio = $(`input[value="${userAnswer.answer_id}"]`);
                if (answerRadio.length > 0) {
                    answerRadio.prop('checked', true);
                    selectedAnswerId = userAnswer.answer_id;
                    syncButtonState();
                    // 如果答案正确，显示完成状态
                    if (userAnswer.is_correct == 1) {
                        showCompletionStatus();
                    }
                }
            } else {
                // 没有答题记录，禁用按钮
                selectedAnswerId = null;
                syncButtonState();
            }

            // 初始化显示考核tab
            switchTab('exam');
        });

        var player = new Aliplayer({
            id: 'J_prismPlayer',
            source: $('input[name="play_url"]').val(), // 播放地址，可以是第三方点播地址，或阿里云点播服务中的播放地址。
            disableSeek: true,   //禁止进度条拖拽
            showBar:true,
            cover:$('input[name="cover_url"]').val(),
            clickPause:true,
            skinLayoutIgnore:[
                'controlBar.subtitle',
                'controlBar.setting'
            ],
            controlBarVisibility:'always'
        });
        //监听第一次开始播放
        player.on('firstPlay',function(e){
            console.log('The player is playing.');
        });
        var firstPlay = 0;
        // //开始播放
        player.on('play',function(e){
            if(firstPlay == 0){
                firstPlay = 1;
                $.ajax({
                    url: '/Course/finishPlay',
                    type: 'POST',
                    data: {
                        item_id: "{{$courseInfo.item_id}}",
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('开始');
                    },
                    error: function() {
                        // 网络错误
                        showErrorDialog('网络错误，请稍后重试');
                    }
                });
            }
        });
        //完播
        player.on('timeupdate', function(e) {
            var finish_play = $('input[name="finish_play"]').val()/100;
            if (player.getCurrentTime() / player.getDuration() >= finish_play) {
                //播放完成
                $.ajax({
                    url: '/Course/finishPlay',
                    type: 'POST',
                    data: {
                        item_id: "{{$courseInfo.item_id}}",
                        finished:1
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('完播记录');
                    },
                    error: function() {
                        // 网络错误
                        showErrorDialog('网络错误，请稍后重试');
                    }
                });
               //关闭监听事件
                player.off('timeupdate');
            }
        });
    </script>
</body>


</html>