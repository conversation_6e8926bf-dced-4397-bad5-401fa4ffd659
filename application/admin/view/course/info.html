<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{:Config('app_name')}} - 课程详情</title>
    {{include file="common/content"}}
    <link rel="stylesheet" href="__PUBLIC__/css-build/course.css">
    <link rel="stylesheet" href="https://g.alicdn.com/apsara-media-box/imp-web-player/2.25.1/skins/default/aliplayer-min.css" />
    <script charset="utf-8" type="text/javascript" src="https://g.alicdn.com/apsara-media-box/imp-web-player/2.25.1/aliplayer-min.js"></script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>

<body class="bg-gray-50">

    <!--顶部返回区域-->
    {{if $accountInfo.type == 1 || $accountInfo.type == 2}}
    <div class="fixed top-0 left-0 w-full bg-white border-b border-gray-200 z-40">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center">
                <button id="back-btn" class="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="ri-arrow-left-line text-xl text-gray-600"></i>
                </button>
                <span class="ml-3 text-lg font-medium text-gray-800">课程详情</span>
            </div>
            <div class="flex items-center space-x-2">
                {{if $accountInfo.type == 1}}
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">经销商</span>
                {{elseif $accountInfo.type == 2}}
                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">群管</span>
                {{/if}}
            </div>
        </div>
    </div>
    {{/if}}

    <!-- 主内容区域 -->
    <div class="{{if $accountInfo.type == 1 || $accountInfo.type == 2}}pt-16{{/if}} pb-24 px-4">
        <!-- 用户信息 -->
        <div class="flex items-center mt-4">
            <div class="w-12 h-12 rounded-full overflow-hidden">
                <img src="{{$accountInfo.avatar}}"
                    alt="用户头像" class="w-full h-full object-cover">
            </div>
            <div class="ml-3">
                <div class="text-base font-medium">{{:base64_decode($accountInfo['user_name'])}}</div>
                <div class="text-xs text-gray-500">ID:{{$accountInfo.user_id}}</div>
            </div>
        </div>
        <!-- 课程视频封面 -->
        <div class="mt-4 relative rounded-lg overflow-hidden shadow-sm">
            <div id="J_prismPlayer">
                <input type="hidden" name="play_url" value="{{$playURL}}">
                <input type="hidden" name="cover_url" value="{{$coverURL}}">
                <input type="hidden" name="finish_play" value="{{$courseInfo.finish_play}}">
            </div>
        </div>
        <!-- 课程标题 -->
        <div class="mt-4 text-xl font-bold">{{$courseInfo.item_name}}</div>
        <!-- 选项卡导航 -->
        <div class="mt-6 flex border-b border-gray-200">
            <div class="tab-active pb-2 mr-8 cursor-pointer" data-tab="exam">考核</div>
            <div class="text-gray-500 pb-2 cursor-pointer" data-tab="intro">课程介绍</div>
        </div>
        <!-- 考核内容区域 -->
        <div id="exam-content">
            <!-- 进度显示 -->
            <!-- <div class="mt-6 flex justify-between items-center">
                <div class="text-2xl font-bold text-primary">0</div>
                <div class="flex-1 mx-4">
                    <div class="progress-bar">
                        <div class="progress-value" style="width: 0%"></div>
                    </div>
                </div>
                <div class="text-2xl font-bold">3</div>
            </div> -->
            <!-- 测验内容 -->
            <div class="mt-6">
                <div class="question-container" data-question="1">
                    <div class="bg-orange-100 text-orange-800 px-3 py-1 rounded-md inline-block text-sm mb-3">单选题</div>
                    <div class="text-base mb-6">
                        {{$courseInfo.question_name}}
                    </div>
                    <!-- 选项 -->
                    <div class="space-y-3">
                        {{volist name="courseInfo.question_item" id="vo"}}
                        <div>
                            <input type="radio" name="answer" id="option-{{$vo.item_id}}" value="{{$vo.item_id}}"
                                {{if $userAnswer.answer_id == $vo.item_id}}checked{{/if}}
                                {{if $userAnswer}}disabled{{/if}}>
                            <label for="option-{{$vo.item_id}}" class="radio-label {{if $userAnswer}}cursor-not-allowed opacity-60{{/if}}">
                                <div class="radio-custom"></div>
                                <div class="flex items-center">
                                    <span class="text-gray-500 mr-4">{{$vo.option_name}}</span>
                                    <span>{{$vo.option_value}}</span>
                                    {{if $userAnswer.answer_id == $vo.item_id}}
                                        {{if $vo.is_true == 1}}
                                            <span class="ml-2 text-green-500 text-sm">✓ 正确答案</span>
                                        {{else /}}
                                            <span class="ml-2 text-red-500 text-sm">✗ 您的选择</span>
                                        {{/if}}
                                    {{elseif $userAnswer && $vo.is_true == 1}}
                                        <span class="ml-2 text-green-500 text-sm">✓ 正确答案</span>
                                    {{/if}}
                                </div>
                            </label>
                        </div>
                        {{/volist}}
                    </div>
                </div>
            </div>
        </div>
        <!-- 课程介绍内容区域 -->
        <div id="intro-content" class="hidden">
            <div class="mt-6">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h3 class="text-lg font-medium mb-4">课程简介</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        {{$courseInfo.desc}}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部提交按钮 -->
    {{if $accountInfo.type == 3}}
    <div class="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 p-4" {{if $userAnswer}}style="display: none;"{{/if}}>
        <button id="next-btn"
            class="w-full py-3 bg-gray-300 text-white font-medium rounded-button flex items-center justify-center"
            disabled>
            提交
        </button>
    </div>
    {{/if}}

    <!-- 已答题状态提示 -->
    {{if $accountInfo.type == 3 && $userAnswer}}
    <div class="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 p-4">
        <div class="w-full py-3 text-center text-gray-500 font-medium">
            {{if $userAnswer.is_correct == 1}}
                <div class="flex items-center justify-center">
                    <i class="ri-check-double-line text-green-500 mr-2"></i>
                    <span>您已完成答题，回答正确！</span>
                </div>
            {{else /}}
                <div class="flex items-center justify-center">
                    <i class="ri-close-circle-line text-red-500 mr-2"></i>
                    <span>您已完成答题，每个会员只有一次答题机会</span>
                </div>
            {{/if}}
        </div>
    </div>
    {{/if}}

    <script>
        $(function() {
            // 获取课程项目ID
            const itemId = "{{$courseInfo.item_id}}";

            // 初始化变量
            let userAnswer = JSON.parse('{{:json_encode($userAnswer?:[])}}');
            let selectedAnswerId = userAnswer && userAnswer.answer_id ? userAnswer.answer_id : null;
            const nextButton = $('#next-btn');
            const tabs = $('[data-tab]');
            const examContent = $('#exam-content');
            const introContent = $('#intro-content');
            let hasAnswered = userAnswer && userAnswer.answer_id; // 是否已答题

            // 切换标签页
            function switchTab(tabType) {
                tabs.removeClass('tab-active').addClass('text-gray-500');
                $(`[data-tab="${tabType}"]`).addClass('tab-active').removeClass('text-gray-500');

                if (tabType === 'exam') {
                    examContent.removeClass('hidden');
                    introContent.addClass('hidden');
                    // 已答题用户不显示提交按钮
                    if (hasAnswered) {
                        nextButton.parent().addClass('hidden');
                    } else {
                        nextButton.parent().removeClass('hidden');
                        syncButtonState();
                    }
                } else {
                    examContent.addClass('hidden');
                    introContent.removeClass('hidden');
                    nextButton.parent().addClass('hidden');
                }
            }

            // 同步提交按钮状态
            function syncButtonState() {
                if (selectedAnswerId) {
                    nextButton.prop('disabled', false)
                        .removeClass('bg-gray-300')
                        .addClass('bg-primary');
                } else {
                    nextButton.prop('disabled', true)
                        .removeClass('bg-primary')
                        .addClass('bg-gray-300');
                }
            }

            // 处理单选按钮变化
            $('input[type="radio"]').on('change', function() {
                // 如果已答题，阻止选择变化
                if (hasAnswered) {
                    return false;
                }
                selectedAnswerId = $(this).val();
                syncButtonState();
            });

            // 检查按钮是否存在
            console.log('nextButton元素:', nextButton);
            console.log('nextButton长度:', nextButton.length);

            // 处理提交按钮点击
            if (nextButton.length > 0) {
                nextButton.on('click', function() {
                console.log('提交按钮被点击');
                console.log('hasAnswered:', hasAnswered);
                console.log('selectedAnswerId:', selectedAnswerId);
                console.log('itemId:', itemId);

                // 如果已答题，阻止提交
                if (hasAnswered) {
                    showErrorDialog('您已经答过题了，每个会员只有一次答题机会');
                    return;
                }

                if (!selectedAnswerId) {
                    showErrorDialog('请先选择一个答案');
                    return;
                }

                // 验证数据
                if (!itemId) {
                    showErrorDialog('课程信息错误，请刷新页面重试');
                    return;
                }

                // 显示加载状态
                nextButton.prop('disabled', true)
                    .text('提交中...');

                console.log('开始发送AJAX请求');

                // 发送答题请求
                $.ajax({
                    url: '/Course/submitAnswer',
                    type: 'POST',
                    data: {
                        item_id: itemId,
                        answer_id: selectedAnswerId
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('AJAX请求成功，服务器响应:', res);

                        if (res.code === 200) {
                            console.log('答题提交成功');
                            // 更新用户答题记录
                            userAnswer = res.data;
                            hasAnswered = true;

                            // 答题成功，显示结果并禁用所有选项
                            showResultDialog(res.data.is_correct, res.data);
                            disableAllOptions();
                        } else {
                            console.log('答题提交失败:', res.msg);
                            // 答题失败或已答题
                            showErrorDialog(res.msg || '提交失败，请重试');

                            // 如果是已答题的情况，更新状态
                            if (res.data && res.data.already_answered) {
                                hasAnswered = true;
                                disableAllOptions();
                            }
                        }

                        // 恢复按钮状态
                        nextButton.prop('disabled', false)
                            .text('提交');
                        syncButtonState();
                    },
                    error: function(xhr, status, error) {
                        // 网络错误
                        console.error('AJAX Error:', {xhr: xhr, status: status, error: error});
                        let errorMessage = '网络错误，请稍后重试';

                        if (xhr.status === 404) {
                            errorMessage = '请求的页面不存在';
                        } else if (xhr.status === 500) {
                            errorMessage = '服务器内部错误';
                        } else if (xhr.status === 0) {
                            errorMessage = '网络连接失败，请检查网络';
                        }

                        showErrorDialog(errorMessage);

                        // 恢复按钮状态
                        nextButton.prop('disabled', false)
                            .text('提交');
                        syncButtonState();
                    }
                });
            });

            // 禁用所有选项
            function disableAllOptions() {
                $('input[type="radio"]').prop('disabled', true);
                $('.radio-label').addClass('cursor-not-allowed opacity-60');
                nextButton.parent().hide();
            }

            // 显示结果对话框
            function showResultDialog(isCorrect, resultData) {
                // 创建对话框
                const dialog = $('<div>').addClass('fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50');

                // 设置对话框内容
                let dialogContent = '';
                if (isCorrect) {
                    dialogContent = `
                        <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                                    <i class="ri-check-line ri-2x text-green-500"></i>
                                </div>
                            </div>
                            <h3 class="text-lg font-medium text-center mb-4">回答正确</h3>
                            <p class="text-gray-600 text-center mb-6">恭喜您答对了问题！</p>
                            <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                        </div>
                    `;
                } else {
                    // 使用服务器返回的正确答案文本
                    const correctOptionText = resultData.correct_option_value;

                    dialogContent = `
                        <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                                    <i class="ri-close-line ri-2x text-red-500"></i>
                                </div>
                            </div>
                            <h3 class="text-lg font-medium text-center mb-4">回答错误</h3>
                            <p class="text-gray-600 text-center mb-2">正确答案是：</p>
                            <p class="text-primary text-center font-medium mb-6">${correctOptionText}</p>
                            <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                        </div>
                    `;
                }

                dialog.html(dialogContent);

                // 添加到页面
                $('body').append(dialog);

                // 绑定关闭事件
                dialog.find('.close-dialog').on('click', function() {
                    dialog.remove();
                    // 答题完成后不再自动跳转，保持在当前页面显示结果
                });
            }

            // 显示错误对话框
            function showErrorDialog(message) {
                const dialog = $('<div>').addClass('fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50');

                dialog.html(`
                    <div class="bg-white p-6 rounded-lg w-4/5 max-w-sm">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                                <i class="ri-error-warning-line ri-2x text-red-500"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-center mb-4">提交失败</h3>
                        <p class="text-gray-600 text-center mb-6">${message}</p>
                        <button class="w-full py-2 bg-primary text-white rounded-button close-dialog">确定</button>
                    </div>
                `);

                $('body').append(dialog);

                dialog.find('.close-dialog').on('click', function() {
                    dialog.remove();
                });
            }

            // 显示完成状态
            function showCompletionStatus() {
                // 隐藏答题区域
                examContent.html(`
                    <div class="mt-10 text-center">
                        <div class="flex items-center justify-center">
                            <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="ri-check-double-line ri-3x text-green-500"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-medium mt-4">课程完成</h3>
                        <p class="text-gray-500 mt-2">您已成功完成本课程的学习</p>
                    </div>
                `);

                // 隐藏提交按钮
                nextButton.parent().addClass('hidden');
            }

            // 标签页切换事件
            tabs.on('click', function() {
                const tabType = $(this).data('tab');
                switchTab(tabType);
            });

            // 如果有用户答题记录，初始化选中状态
            if (hasAnswered) {
                // 选中用户之前选择的答案
                const answerRadio = $(`input[value="${userAnswer.answer_id}"]`);
                if (answerRadio.length > 0) {
                    answerRadio.prop('checked', true);
                    selectedAnswerId = userAnswer.answer_id;
                }
                // 禁用所有选项
                disableAllOptions();
            } else {
                // 没有答题记录，禁用按钮
                selectedAnswerId = null;
                syncButtonState();
            }

            // 初始化显示考核tab
            switchTab('exam');

            // 返回按钮点击事件（仅经销商和群管可见）
            $('#back-btn').on('click', function() {
                // 返回上一页
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // 如果没有历史记录，根据用户类型跳转到相应页面
                    const userType = "{{$accountInfo.type}}";
                    if (userType == "1") {
                        // 经销商返回到内容库
                        window.location.href = '/content/index';
                    } else if (userType == "2") {
                        // 群管返回到内容库
                        window.location.href = '/content/index';
                    }
                }
            });
            } else {
                console.log('提交按钮不存在，可能是因为用户类型不是会员');
            }
        });

        var player = new Aliplayer({
            id: 'J_prismPlayer',
            source: $('input[name="play_url"]').val(), // 播放地址，可以是第三方点播地址，或阿里云点播服务中的播放地址。
            disableSeek: true,   //禁止进度条拖拽
            showBar:true,
            cover:$('input[name="cover_url"]').val(),
            clickPause:true,
            skinLayoutIgnore:[
                'controlBar.subtitle',
                'controlBar.setting'
            ],
            controlBarVisibility:'always'
        });
        //监听第一次开始播放
        player.on('firstPlay',function(e){
            console.log('The player is playing.');
        });
        var firstPlay = 0;
        // //开始播放
        player.on('play',function(e){
            if(firstPlay == 0){
                firstPlay = 1;
                $.ajax({
                    url: '/Course/finishPlay',
                    type: 'POST',
                    data: {
                        item_id: "{{$courseInfo.item_id}}",
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('开始');
                    },
                    error: function() {
                        // 网络错误
                        showErrorDialog('网络错误，请稍后重试');
                    }
                });
            }
        });
        //完播
        player.on('timeupdate', function(e) {
            var finish_play = $('input[name="finish_play"]').val()/100;
            if (player.getCurrentTime() / player.getDuration() >= finish_play) {
                //播放完成
                $.ajax({
                    url: '/Course/finishPlay',
                    type: 'POST',
                    data: {
                        item_id: "{{$courseInfo.item_id}}",
                        finished:1
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('完播记录');
                    },
                    error: function() {
                        // 网络错误
                        showErrorDialog('网络错误，请稍后重试');
                    }
                });
               //关闭监听事件
                player.off('timeupdate');
            }
        });
    </script>
</body>


</html>