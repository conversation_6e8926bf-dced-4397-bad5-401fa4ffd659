<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{:Config('app_name')}} - 内容库</title>
    {{include file="common/content"}}
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .search-input:focus {
            outline: none;
        }
        .course-card {
            transition: all 0.3s ease;
        }
        .course-card:active {
            transform: scale(0.98);
        }
        .category-active {
            color: #1e88e5;
            background-color: #1e88e5;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100">

    <div class="pb-16">
        {{notempty name="periodRows"}}


        <!-- 搜索框 -->
        <!-- <div class="w-full bg-white z-10 px-4 py-4">
            <div class="relative">
                <input type="text" class="search-input w-full pl-4 pr-4 py-3 bg-gray-100 rounded-lg text-sm border-none" placeholder="点击选择训练营">
            </div>
        </div> -->

        <!-- 分类标签 -->
        <div class="w-full bg-white z-10 border-b py-3">
            <div class="flex overflow-x-auto whitespace-nowrap px-4 py-2 hide-scrollbar">
                <button class="px-3 py-1.5 mr-3 text-sm rounded-full {{if $filter['period_id'] == 0}} category-active {{else /}} bg-gray-100 {{/if}} ">
                    <a href="{{:url('index')}}">全部</a>
                </button>
                {{volist name="periodRows" id="vo"}}
                    <button class="px-3 py-1.5 mr-3 text-sm rounded-full {{if $filter['period_id'] == $vo['period_id']}} category-active {{else /}} bg-gray-100 {{/if}}">
                        <a href="{{:url('index',array('period_id'=>$vo['period_id']))}}">{{$vo.period_name}}</a>
                    </button>
                {{/volist}}
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="pt-3 pb-20 px-4 content">
            {{volist name="items" id="vo"}}
            <!-- 课程卡片 -->
            <div class="course-card bg-white rounded-lg shadow-sm overflow-hidden mb-4 cursor-pointer">
                <a href="{{:url('/course/info',['item_id'=>$vo['item_id']])}}">
                    <div class="relative">
                        {{notempty name="vo.cover_url"}}
                            <img src="{{:get_img_url('course',$vo['cover_url'])}}" alt="课程封面" class="w-full h-40 object-cover">
                        {{else /}}
                            <img src="__PUBLIC__/img/course_cover.jpg" alt="课程封面" class="w-full h-40 object-cover">
                        {{/notempty}}
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white">
                            <h3 class="text-xl font-medium">{{$vo.item_name}}</h3>
                            <div class="flex justify-between mt-1">
                                <p class="text-sm">时长: {{$vo.duration}}</p>
                                <p class="text-sm">{{:date('Y-m-d',$vo['start_time'])}} {{$vo.start_time|date='H:i'}} - {{$vo.end_time|date='H:i'}}</p>
                            </div>
                        </div>
                        <div class="absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded">{{$vo.period_name}}</div>
                    </div>
                </a>
            </div>
            {{/volist}}
        </div>
        {{else/}}
        <!-- 提示暂无内容-->
        <div class="fixed inset-0 flex items-center justify-center bg-gray-50 z-index-10">
            <div class="flex flex-col items-center justify-center text-center px-8">
                <div class="w-32 h-32 flex items-center justify-center mb-6">
                    <img src="__PUBLIC__/img/empty.jpg" alt="空状态图标" class="w-full h-full object-contain opacity-60">
                </div>
                <div class="text-gray-600 text-lg font-medium">暂无内容</div>
                <div class="text-gray-400 text-sm mt-2">当前没有可显示的内容</div>
            </div>
        </div>
        {{/notempty}}
    </div>

    <!-- 底部标签栏 -->
    {{include file="common/footer"}}

    <script>
        // 搜索框点击事件
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('click', function() {
            const searchModal = document.createElement('div');
            searchModal.className = 'fixed inset-0 bg-white z-50 flex flex-col';
            searchModal.innerHTML = `
                <div class="flex items-center px-4 py-3 border-b">
                    <div class="w-8 h-8 flex items-center justify-center cursor-pointer mr-2">
                        <i class="ri-arrow-left-line ri-lg"></i>
                    </div>
                    <div class="flex-1 relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-search-line text-gray-400"></i>
                        </div>
                        <input type="text" class="search-input w-full pl-10 pr-4 py-2 bg-gray-100 rounded-lg text-sm border-none" placeholder="搜索训练营">
                    </div>
                </div>
                <div class="flex-1 p-4">
                    <h3 class="text-base font-medium mb-3">热门训练营</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-gray-100 rounded-lg p-3 cursor-pointer">
                            <h4 class="text-sm font-medium">高效工作训练营</h4>
                            <p class="text-xs text-gray-500 mt-1">8节课 · 4月15日开营</p>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-3 cursor-pointer">
                            <h4 class="text-sm font-medium">职场沟通训练营</h4>
                            <p class="text-xs text-gray-500 mt-1">10节课 · 4月20日开营</p>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-3 cursor-pointer">
                            <h4 class="text-sm font-medium">领导力提升训练营</h4>
                            <p class="text-xs text-gray-500 mt-1">12节课 · 5月5日开营</p>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-3 cursor-pointer">
                            <h4 class="text-sm font-medium">数据分析训练营</h4>
                            <p class="text-xs text-gray-500 mt-1">8节课 · 5月10日开营</p>
                        </div>
                    </div>

                    <h3 class="text-base font-medium mt-6 mb-3">历史搜索</h3>
                    <div class="flex flex-wrap">
                        <div class="bg-gray-100 rounded-full px-3 py-1 text-sm mr-2 mb-2 cursor-pointer">高效工作</div>
                        <div class="bg-gray-100 rounded-full px-3 py-1 text-sm mr-2 mb-2 cursor-pointer">职场沟通</div>
                        <div class="bg-gray-100 rounded-full px-3 py-1 text-sm mr-2 mb-2 cursor-pointer">领导力</div>
                        <div class="bg-gray-100 rounded-full px-3 py-1 text-sm mr-2 mb-2 cursor-pointer">数据分析</div>
                    </div>
                </div>
            `;
            document.body.appendChild(searchModal);

            const backBtn = searchModal.querySelector('.ri-arrow-left-line');
            backBtn.parentElement.addEventListener('click', function() {
                document.body.removeChild(searchModal);
            });

            const trainingCamps = searchModal.querySelectorAll('.bg-gray-100.rounded-lg');
            trainingCamps.forEach(camp => {
                camp.addEventListener('click', function() {
                    const campName = this.querySelector('h4').textContent;
                    searchInput.value = campName;
                    document.body.removeChild(searchModal);

                    // 显示选择成功提示
                    const successToast = document.createElement('div');
                    successToast.className = 'fixed top-16 left-1/2 transform -translate-x-1/2 bg-primary text-white px-4 py-2 rounded-lg z-50';
                    successToast.textContent = `已选择: ${campName}`;
                    document.body.appendChild(successToast);

                    setTimeout(() => {
                        document.body.removeChild(successToast);
                    }, 2000);
                });
            });

            const historyTags = searchModal.querySelectorAll('.bg-gray-100.rounded-full');
            historyTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const tagText = this.textContent;
                    searchInput.value = tagText;
                    document.body.removeChild(searchModal);
                });
            });
        });

        // 分类标签切换
        const categoryButtons = document.querySelectorAll('.rounded-full');
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                categoryButtons.forEach(btn => {
                    btn.classList.remove('category-active');
                    btn.classList.add('bg-gray-100');
                });
                this.classList.remove('bg-gray-100');
                this.classList.add('category-active');
            });
        });

        // 课程卡片点击事件
        const courseCards = document.querySelectorAll('.course-card');
        courseCards.forEach(card => {
            card.addEventListener('click', function() {
                const courseTitle = this.querySelector('h3').textContent;
                const courseDuration = this.querySelector('.flex.justify-between p:first-child').textContent;

                const courseModal = document.createElement('div');
                courseModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                courseModal.innerHTML = `
                    <div class="bg-white rounded-lg w-5/6 overflow-hidden">
                        <div class="relative">
                            <img src="${this.querySelector('img').src}" alt="课程封面" class="w-full h-48 object-cover">
                            <div class="absolute top-0 left-0 w-full p-3 flex justify-between items-center">
                                <div class="w-8 h-8 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer">
                                    <i class="ri-arrow-left-line text-white"></i>
                                </div>
                                <div class="flex space-x-2">
                                    <div class="w-8 h-8 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer">
                                        <i class="ri-share-forward-line text-white"></i>
                                    </div>
                                    <div class="w-8 h-8 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer">
                                        <i class="ri-more-2-fill text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-medium">${courseTitle}</h3>
                            <p class="text-sm text-gray-500 mt-1">${courseDuration}</p>

                            <div class="mt-4">
                                <h4 class="text-base font-medium mb-2">课程简介</h4>
                                <p class="text-sm text-gray-600">本节课将深入探讨如何提高工作效率和时间管理能力，帮助你在职场中脱颖而出。通过实用的工具和方法，让你的工作更加高效、有序。</p>
                            </div>

                            <div class="mt-4">
                                <h4 class="text-base font-medium mb-2">课程大纲</h4>
                                <div class="text-sm text-gray-600">
                                    <p class="mb-1">1. 高效工作的核心原则 (10分钟)</p>
                                    <p class="mb-1">2. 时间管理四象限法则 (12分钟)</p>
                                    <p class="mb-1">3. 工作计划与执行技巧 (15分钟)</p>
                                    <p>4. 案例分析与实践应用 (5分钟)</p>
                                </div>
                            </div>
                        </div>
                        <div class="border-t p-4">
                            <button class="w-full py-3 bg-primary text-white rounded-button text-base font-medium">开始学习</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(courseModal);

                const backBtn = courseModal.querySelector('.ri-arrow-left-line');
                backBtn.parentElement.addEventListener('click', function() {
                    document.body.removeChild(courseModal);
                });

                // 点击背景关闭
                courseModal.addEventListener('click', function(e) {
                    if (e.target === courseModal) {
                        document.body.removeChild(courseModal);
                    }
                });

                // 开始学习按钮点击
                const startBtn = courseModal.querySelector('.bg-primary');
                startBtn.addEventListener('click', function() {
                    document.body.removeChild(courseModal);

                    // 创建视频播放页面
                    const videoPage = document.createElement('div');
                    videoPage.className = 'fixed inset-0 bg-black z-50 flex flex-col';
                    videoPage.innerHTML = `
                        <div class="relative w-full" style="height: 211px;">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="${card.querySelector('img').src}" alt="视频封面" class="w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                                    <div class="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                                        <i class="ri-play-fill ri-2x text-white"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="absolute top-0 left-0 w-full p-3 flex justify-between items-center">
                                <div class="w-8 h-8 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer" id="closeVideo">
                                    <i class="ri-arrow-left-line text-white"></i>
                                </div>
                                <div class="flex space-x-2">
                                    <div class="w-8 h-8 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer">
                                        <i class="ri-fullscreen-line text-white"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="absolute bottom-0 left-0 w-full px-4 py-2">
                                <div class="w-full bg-gray-600 h-1 rounded-full overflow-hidden">
                                    <div class="bg-primary h-full w-1/4 rounded-full"></div>
                                </div>
                                <div class="flex justify-between text-white text-xs mt-1">
                                    <span>10:15</span>
                                    <span>${courseDuration.replace('时长: ', '')}</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1 bg-white p-4 overflow-y-auto">
                            <h3 class="text-lg font-medium">${courseTitle}</h3>
                            <p class="text-sm text-gray-500 mt-1">${courseDuration}</p>

                            <div class="mt-4 flex space-x-4">
                                <button class="flex-1 py-2 bg-gray-100 rounded-full text-sm flex items-center justify-center">
                                    <i class="ri-download-line mr-1"></i>
                                    <span>下载</span>
                                </button>
                                <button class="flex-1 py-2 bg-gray-100 rounded-full text-sm flex items-center justify-center">
                                    <i class="ri-share-forward-line mr-1"></i>
                                    <span>分享</span>
                                </button>
                                <button class="flex-1 py-2 bg-gray-100 rounded-full text-sm flex items-center justify-center">
                                    <i class="ri-star-line mr-1"></i>
                                    <span>收藏</span>
                                </button>
                            </div>

                            <div class="mt-6">
                                <h4 class="text-base font-medium mb-3">课程笔记</h4>
                                <textarea class="w-full border border-gray-200 rounded-lg p-3 text-sm" rows="4" placeholder="记录你的学习心得..."></textarea>
                                <button class="mt-2 px-4 py-2 bg-primary text-white rounded-button text-sm">保存笔记</button>
                            </div>

                            <div class="mt-6">
                                <h4 class="text-base font-medium mb-3">相关推荐</h4>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-gray-100 rounded-lg p-3">
                                        <h5 class="text-sm font-medium">第08节课</h5>
                                        <p class="text-xs text-gray-500 mt-1">即将上线</p>
                                    </div>
                                    <div class="bg-gray-100 rounded-lg p-3">
                                        <h5 class="text-sm font-medium">第06节课</h5>
                                        <p class="text-xs text-gray-500 mt-1">40分19秒</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(videoPage);

                    const closeVideoBtn = videoPage.querySelector('#closeVideo');
                    closeVideoBtn.addEventListener('click', function() {
                        document.body.removeChild(videoPage);
                    });
                });
            });
        });

        // 关闭按钮点击事件
        const closeBtn = document.querySelector('.ri-close-line');
        closeBtn.parentElement.addEventListener('click', function() {
            // 创建确认弹窗
            const confirmModal = document.createElement('div');
            confirmModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            confirmModal.innerHTML = `
                <div class="bg-white rounded-lg w-4/5 p-4">
                    <h3 class="text-lg font-medium text-center mb-3">确定要退出吗？</h3>
                    <p class="text-sm text-gray-600 text-center mb-4">退出后将返回上一页</p>
                    <div class="flex space-x-3">
                        <button class="flex-1 py-2 bg-gray-200 rounded-button text-gray-600">取消</button>
                        <button class="flex-1 py-2 bg-primary text-white rounded-button">确定</button>
                    </div>
                </div>
            `;
            document.body.appendChild(confirmModal);

            const cancelBtn = confirmModal.querySelector('.bg-gray-200');
            cancelBtn.addEventListener('click', function() {
                document.body.removeChild(confirmModal);
            });

            const confirmBtn = confirmModal.querySelector('.bg-primary');
            confirmBtn.addEventListener('click', function() {
                window.history.back();
            });

            // 点击背景关闭
            confirmModal.addEventListener('click', function(e) {
                if (e.target === confirmModal) {
                    document.body.removeChild(confirmModal);
                }
            });
        });

        // 更多按钮点击事件
        const moreBtn = document.querySelector('.ri-more-2-fill');
        moreBtn.parentElement.addEventListener('click', function() {
            const actionSheet = document.createElement('div');
            actionSheet.className = 'fixed inset-0 bg-black bg-opacity-50 flex flex-col justify-end z-50';
            actionSheet.innerHTML = `
                <div class="bg-white rounded-t-xl p-4">
                    <h3 class="text-center text-lg font-medium mb-4">更多操作</h3>
                    <div class="grid grid-cols-4 gap-4 mb-4">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-refresh-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">刷新</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-filter-3-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">筛选</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-download-2-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">下载</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                                <i class="ri-share-forward-line ri-lg text-primary"></i>
                            </div>
                            <span class="text-xs">分享</span>
                        </div>
                    </div>
                    <button class="w-full py-3 bg-gray-100 rounded-lg text-base">取消</button>
                </div>
            `;
            document.body.appendChild(actionSheet);

            const cancelBtn = actionSheet.querySelector('.bg-gray-100');
            cancelBtn.addEventListener('click', function() {
                document.body.removeChild(actionSheet);
            });

            // 点击背景关闭
            actionSheet.addEventListener('click', function(e) {
                if (e.target === actionSheet) {
                    document.body.removeChild(actionSheet);
                }
            });

            // 功能按钮点击
            const functionBtns = actionSheet.querySelectorAll('.flex.flex-col.items-center');
            functionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const functionName = this.querySelector('span').textContent;
                    document.body.removeChild(actionSheet);

                    // 显示功能执行提示
                    const toast = document.createElement('div');
                    toast.className = 'fixed top-16 left-1/2 transform -translate-x-1/2 bg-primary text-white px-4 py-2 rounded-lg z-50';
                    toast.textContent = `正在${functionName}...`;
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>